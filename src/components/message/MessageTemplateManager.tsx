import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Plus, Pencil, Trash2, Save, X } from "lucide-react";
import { useMessageTemplates } from "@/hooks/useMessageTemplates";
import { useCreateMessageTemplate } from "@/hooks/useCreateMessageTemplate";
import { useUpdateMessageTemplate } from "@/hooks/useUpdateMessageTemplate";
import { useDeleteMessageTemplate } from "@/hooks/useDeleteMessageTemplate";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";

export function MessageTemplateManager() {
  const { data: templates = [], isLoading } = useMessageTemplates();
  const createTemplate = useCreateMessageTemplate();
  const updateTemplate = useUpdateMessageTemplate();
  const deleteTemplate = useDeleteMessageTemplate();
  
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [formData, setFormData] = useState({
    name: "",
    subject: "",
    content: "",
    template_category: "general" as 'general' | 'interview' | 'follow_up' | 'rejection' | 'offer'
  });

  // Reset form when dialog closes
  useEffect(() => {
    if (!isCreateDialogOpen) {
      setFormData({
        name: "",
        subject: "",
        content: "",
        template_category: "general"
      });
    }
  }, [isCreateDialogOpen]);

  // Set form data when editing
  useEffect(() => {
    if (selectedTemplate) {
      setFormData({
        name: selectedTemplate.name,
        subject: selectedTemplate.subject,
        content: selectedTemplate.content,
        template_category: selectedTemplate.template_category
      });
    }
  }, [selectedTemplate]);

  const handleCreateTemplate = async () => {
    try {
      await createTemplate.mutateAsync(formData);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error('Error creating template:', error);
    }
  };

  const handleUpdateTemplate = async () => {
    if (!selectedTemplate) return;
    
    try {
      await updateTemplate.mutateAsync({
        id: selectedTemplate.id,
        ...formData
      });
      setIsEditDialogOpen(false);
      setSelectedTemplate(null);
    } catch (error) {
      console.error('Error updating template:', error);
    }
  };

  const handleDeleteTemplate = async (id: string) => {
    try {
      await deleteTemplate.mutateAsync(id);
    } catch (error) {
      console.error('Error deleting template:', error);
    }
  };

  const handleEditClick = (template: any) => {
    setSelectedTemplate(template);
    setIsEditDialogOpen(true);
  };

  const renderTemplateList = (category: string) => {
    const filteredTemplates = templates.filter(
      template => template.template_category === category || (category === 'all' && template)
    );

    if (filteredTemplates.length === 0) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          No templates found in this category
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="hover:bg-accent/5 transition-colors">
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-medium">{template.name}</h3>
                <div className="flex gap-2">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => handleEditClick(template)}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => handleDeleteTemplate(template.id)}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mb-2">
                Subject: {template.subject}
              </p>
              <div className="text-sm text-muted-foreground whitespace-pre-line line-clamp-3">
                {template.content}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Message Templates</CardTitle>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Template
        </Button>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all">
          <TabsList className="mb-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="interview">Interview</TabsTrigger>
            <TabsTrigger value="follow_up">Follow-up</TabsTrigger>
            <TabsTrigger value="offer">Offer</TabsTrigger>
            <TabsTrigger value="rejection">Rejection</TabsTrigger>
          </TabsList>
          
          <ScrollArea className="h-[500px]">
            <TabsContent value="all">
              {isLoading ? (
                <div className="text-center py-8">Loading templates...</div>
              ) : (
                renderTemplateList('all')
              )}
            </TabsContent>
            <TabsContent value="general">
              {renderTemplateList('general')}
            </TabsContent>
            <TabsContent value="interview">
              {renderTemplateList('interview')}
            </TabsContent>
            <TabsContent value="follow_up">
              {renderTemplateList('follow_up')}
            </TabsContent>
            <TabsContent value="offer">
              {renderTemplateList('offer')}
            </TabsContent>
            <TabsContent value="rejection">
              {renderTemplateList('rejection')}
            </TabsContent>
          </ScrollArea>
        </Tabs>
      </CardContent>

      {/* Create Template Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Message Template</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Template Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="e.g., Interview Invitation"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formData.template_category}
                  onValueChange={(value: any) => setFormData({...formData, template_category: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="interview">Interview</SelectItem>
                    <SelectItem value="follow_up">Follow-up</SelectItem>
                    <SelectItem value="offer">Offer</SelectItem>
                    <SelectItem value="rejection">Rejection</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                value={formData.subject}
                onChange={(e) => setFormData({...formData, subject: e.target.value})}
                placeholder="e.g., Interview Invitation for [Position]"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="content">Content</Label>
              <Textarea
                id="content"
                value={formData.content}
                onChange={(e) => setFormData({...formData, content: e.target.value})}
                placeholder="Enter template content..."
                className="min-h-[200px]"
              />
              <p className="text-xs text-muted-foreground">
                Use placeholders like [Name], [Position], [Company], etc. that will be replaced when using the template.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleCreateTemplate}
              disabled={createTemplate.isPending}
            >
              {createTemplate.isPending ? "Creating..." : "Create Template"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Template Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Message Template</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Template Name</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-category">Category</Label>
                <Select
                  value={formData.template_category}
                  onValueChange={(value: any) => setFormData({...formData, template_category: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="interview">Interview</SelectItem>
                    <SelectItem value="follow_up">Follow-up</SelectItem>
                    <SelectItem value="offer">Offer</SelectItem>
                    <SelectItem value="rejection">Rejection</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-subject">Subject</Label>
              <Input
                id="edit-subject"
                value={formData.subject}
                onChange={(e) => setFormData({...formData, subject: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-content">Content</Label>
              <Textarea
                id="edit-content"
                value={formData.content}
                onChange={(e) => setFormData({...formData, content: e.target.value})}
                className="min-h-[200px]"
              />
              <p className="text-xs text-muted-foreground">
                Use placeholders like [Name], [Position], [Company], etc. that will be replaced when using the template.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleUpdateTemplate}
              disabled={updateTemplate.isPending}
            >
              {updateTemplate.isPending ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}