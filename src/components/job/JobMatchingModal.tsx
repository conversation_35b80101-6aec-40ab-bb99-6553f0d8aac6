import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useNavigate } from "react-router-dom";
import { Briefcase, MapPin, Star, Mail } from "lucide-react";

interface JobMatch {
  id: string;
  name: string;
  match: number;
  role?: string;
  location?: string;
  experience?: string;
  skills?: string[];
  avatar?: string;
  email?: string;
}

interface JobMatchingModalProps {
  isOpen: boolean;
  onClose: () => void;
  matches: JobMatch[];
  jobTitle: string;
}

export function JobMatchingModal({ isOpen, onClose, matches, jobTitle }: JobMatchingModalProps) {
  const navigate = useNavigate();

  const handleViewProfile = (candidateId: string) => {
    navigate(`/candidates/${candidateId}`);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Matching Candidates</DialogTitle>
          <DialogDescription>
            Found {matches.length} potential candidates for {jobTitle}
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-[60vh] pr-4">
          <div className="space-y-4">
            {matches.map((candidate) => (
              <div
                key={candidate.id}
                className="flex items-start justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
              >
                <div className="flex gap-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={candidate.avatar} alt={candidate.name} />
                    <AvatarFallback>{candidate.name.split(" ").map(n => n[0]).join("")}</AvatarFallback>
                  </Avatar>
                  <div className="space-y-2">
                    <div>
                      <h4 className="font-medium">{candidate.name}</h4>
                      {candidate.role && (
                        <p className="text-sm text-muted-foreground">{candidate.role}</p>
                      )}
                    </div>
                    <div className="flex gap-4 text-sm text-muted-foreground">
                      {candidate.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4" />
                          {candidate.location}
                        </div>
                      )}
                      {candidate.experience && (
                        <div className="flex items-center gap-1">
                          <Briefcase className="w-4 h-4" />
                          {candidate.experience}
                        </div>
                      )}
                    </div>
                    {candidate.skills && (
                      <div className="flex flex-wrap gap-1">
                        {candidate.skills.map((skill) => (
                          <Badge key={skill} variant="secondary" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <Badge variant="secondary" className="mb-2">
                    {candidate.match}% Match
                  </Badge>
                  <div className="flex gap-2">
                    {candidate.email && (
                      <Button variant="outline" size="sm" className="h-8">
                        <Mail className="w-4 h-4 mr-1" />
                        Contact
                      </Button>
                    )}
                    <Button
                      size="sm"
                      className="h-8"
                      onClick={() => handleViewProfile(candidate.id)}
                    >
                      View Profile
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}