
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Brain, Clock, Tag, Bell, LayoutList, LayoutGrid, CalendarRange, CalendarDays, Calendar as CalendarIcon } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import { useDeleteEvent } from "@/hooks/useDeleteEvent";

import { useEvents, Event } from "@/hooks/useEvents";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useEffect, useState } from "react";

export const UpcomingEvents = ({ events: propEvents, onDeleteEvent }: { events?: Event[], onDeleteEvent?: (eventId: string) => void }) => {
  const { toast } = useToast();
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'timeline' | 'calendar'>('list');
  // Use propEvents if provided, otherwise useEvents hook
  const { data: events = [], isLoading: loading, error } = useEvents();
  const allEvents = propEvents || events;
  const deleteEvent = useDeleteEvent();

  // Filter upcoming events (next 7 days)
  const upcomingEvents = (allEvents as Event[]).filter(event => {
    const eventDate = new Date(event.start_time);
    const now = new Date();
    const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    return eventDate >= now && eventDate <= weekFromNow;
  });

  const generateMeetingSummary = (eventId: string) => {
    toast({
      title: "AI Summary Assistant",
      description: "Generating meeting summary...",
    });

    setTimeout(() => {
      toast({
        title: "Meeting Summary Generated",
        description: "Key points and action items have been analyzed and summarized.",
      });
    }, 1500);
  };

  const handleSetReminder = async (eventId: string) => {
    toast({
      title: "Reminder Set",
      description: "You'll be notified 15 minutes before the event.",
    });
  };

  const handleUpdateCategory = async (eventId: string) => {
    toast({
      title: "Category Updated",
      description: "Event category has been updated successfully.",
    });
  };

  const handleDeleteEvent = async (eventId: string) => {
    try {
      await deleteEvent.mutateAsync(eventId);
      if (onDeleteEvent) onDeleteEvent(eventId);
    } catch (error) {
      console.error('Error deleting event:', error);
    }
  };
  const renderEvents = () => {
    if (loading) {
      return <div className="text-center p-4">Loading events...</div>;
    }

    if (upcomingEvents.length === 0) {
      return <div className="text-center p-4 text-muted-foreground">No upcoming events in the next 7 days.</div>;
    }

    switch (viewMode) {
      case 'grid':
        return (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {upcomingEvents.map((event) => (
              <div key={event.id} className="p-4 bg-secondary/10 rounded-md hover:bg-secondary/20 transition-colors">
                <div className="flex justify-between items-start">
                  <h3 className="font-medium">{event.title}</h3>
                  <Badge variant={event.priority === 'high' ? 'destructive' : 'secondary'}>
                    {event.priority}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mt-2">{event.description}</p>
                <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  <span>{new Date(event.start_time).toLocaleTimeString()}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-auto"
                    onClick={() => handleDeleteEvent(event.id)}
                  >
                    Delete
                  </Button>
                </div>
              </div>
            ))}
          </div>
        );
      
      case 'timeline':
        return (
          <div className="space-y-4 relative before:absolute before:left-2 before:top-0 before:bottom-0 before:w-0.5 before:bg-border">
            {upcomingEvents.map((event) => (
              <div key={event.id} className="pl-8 relative">
                <div className="absolute left-0 top-2 w-4 h-4 rounded-full bg-primary"></div>
                <div className="p-4 bg-secondary/10 rounded-md hover:bg-secondary/20 transition-colors">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{event.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {new Date(event.start_time).toLocaleDateString()} at {new Date(event.start_time).toLocaleTimeString()}
                      </p>
                    </div>
                    <Badge variant={event.priority === 'high' ? 'destructive' : 'secondary'}>
                      {event.priority}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">{event.description}</p>
                  <div className="flex justify-end mt-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteEvent(event.id)}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        );

      case 'calendar':
        return (
          <div className="grid grid-cols-7 gap-1">
            {Array.from({ length: 7 }, (_, i) => (
              <div key={i} className="text-center text-sm font-medium p-2">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][i]}
              </div>
            ))}
            {Array.from({ length: 35 }, (_, i) => (
              <div key={`day-${i}`} className="aspect-square p-1 border rounded-md">
                <div className="text-xs text-right text-muted-foreground">{i + 1}</div>
                {upcomingEvents.some(e => new Date(e.start_time).getDate() === (i + 1)) && (
                  <div className="mt-1">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                )}
              </div>
            ))}
          </div>
        );

      default: // list view
        return (
          <div className="space-y-4">
            {upcomingEvents.map((event) => (
              <div key={event.id} className="flex flex-col space-y-2 p-4 bg-secondary/10 rounded-md hover:bg-secondary/20 transition-colors">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{event.title}</h3>
                      <Badge variant={event.priority === 'high' ? 'destructive' : 'secondary'}>
                        {event.priority}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {new Date(event.start_time).toLocaleDateString()} at {new Date(event.start_time).toLocaleTimeString()}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => generateMeetingSummary(event.id)}
                    >
                      <Brain className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSetReminder(event.id)}
                    >
                      <Bell className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleUpdateCategory(event.id)}
                    >
                      <Tag className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteEvent(event.id)}
                      className="text-destructive"
                    >
                      Delete
                    </Button>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">{event.description}</p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  <span>
                    Duration: {Math.round((new Date(event.end_time).getTime() - new Date(event.start_time).getTime()) / (1000 * 60))} minutes
                  </span>
                  <Badge variant="outline" className="ml-2">
                    {event.category}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        );
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Upcoming Events</span>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                {viewMode === 'list' && <LayoutList className="h-4 w-4 mr-2" />}
                {viewMode === 'grid' && <LayoutGrid className="h-4 w-4 mr-2" />}
                {viewMode === 'timeline' && <CalendarRange className="h-4 w-4 mr-2" />}
                {viewMode === 'calendar' && <CalendarDays className="h-4 w-4 mr-2" />}
                View as {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setViewMode('list')}>
                <LayoutList className="h-4 w-4 mr-2" />
                List View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setViewMode('grid')}>
                <LayoutGrid className="h-4 w-4 mr-2" />
                Grid View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setViewMode('timeline')}>
                <CalendarRange className="h-4 w-4 mr-2" />
                Timeline View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setViewMode('calendar')}>
                <CalendarDays className="h-4 w-4 mr-2" />
                Calendar View
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {renderEvents()}
      </CardContent>
    </Card>
  );
};
