import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { MapPin, Building, Clock, CheckCircle2 } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface JobMatchCardProps {
  job: {
    id: string;
    title: string;
    company: string;
    location: string;
    match: number;
    status: string;
  };
  onClick: () => void;
}

export function JobMatchCard({ job, onClick }: JobMatchCardProps) {
  const getMatchColor = (match: number) => {
    if (match >= 90) return "text-green-500";
    if (match >= 70) return "text-yellow-500";
    return "text-orange-500";
  };

  return (
    <div
      onClick={onClick}
      className="group flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 cursor-pointer transition-all"
    >
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <h4 className="font-medium group-hover:text-primary transition-colors">
            {job.title}
          </h4>
          {job.match >= 90 && (
            <Tooltip>
              <TooltipTrigger>
                <CheckCircle2 className="w-4 h-4 text-green-500" />
              </TooltipTrigger>
              <TooltipContent>Perfect Match</TooltipContent>
            </Tooltip>
          )}
        </div>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <MapPin className="w-4 h-4" />
            {job.location}
          </div>
          <div className="flex items-center gap-1">
            <Building className="w-4 h-4" />
            {job.company}
          </div>
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            {job.status}
          </div>
        </div>
      </div>
      <div className="text-right">
        <div className={`text-lg font-semibold ${getMatchColor(job.match)}`}>
          {job.match}%
        </div>
        <Progress value={job.match} className="w-20 h-1.5 mt-1" />
      </div>
    </div>
  );
}