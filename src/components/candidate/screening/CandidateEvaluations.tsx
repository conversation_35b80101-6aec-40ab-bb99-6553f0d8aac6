import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";

interface EvaluationType {
  category: string;
  score: number;
  notes: string;
}

interface CandidateEvaluationsProps {
  evaluations: EvaluationType[];
}

export function CandidateEvaluations({ evaluations }: CandidateEvaluationsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Evaluation Scores</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {evaluations.map((evaluation, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">{evaluation.category}</span>
                <span className="text-sm">{evaluation.score}/5</span>
              </div>
              <div className="h-2 bg-secondary rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary"
                  style={{ width: `${(evaluation.score / 5) * 100}%` }}
                />
              </div>
              <p className="text-sm text-muted-foreground">{evaluation.notes}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}