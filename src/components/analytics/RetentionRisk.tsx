import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { TrendingDown, Loader2 } from "lucide-react";
import { useRetention } from "@/hooks/useRetention";
import { Skeleton } from "@/components/ui/skeleton";

export const RetentionRisk = () => {
  const { data: retentionPredictions = [], isLoading, error } = useRetention();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-4 w-4" />
            <Skeleton className="h-6 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center justify-between p-2 border rounded">
                <Skeleton className="h-5 w-24" />
                <div className="flex items-center gap-4">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-6 w-20" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-4 w-4" />
            AI-Predicted Retention Risk
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center p-8">
            <p className="text-muted-foreground">Failed to load retention data. Please try again.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingDown className="h-4 w-4" />
          AI-Predicted Retention Risk
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {retentionPredictions.map((dept, index) => (
            <div key={index} className="flex items-center justify-between p-2 border rounded">
              <span className="font-medium">{dept.department}</span>
              <div className="flex items-center gap-4">
                <span className="text-sm">Score: {dept.score}</span>
                <span className={`px-2 py-1 rounded text-xs ${
                  dept.risk_level === 'Low' ? 'bg-green-100 text-green-800' :
                  dept.risk_level === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {dept.risk_level} Risk
                </span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};