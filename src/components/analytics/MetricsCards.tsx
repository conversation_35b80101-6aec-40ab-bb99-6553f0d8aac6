
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Users, Briefcase, Clock, TrendingUp } from "lucide-react";
import { useAnalyticsMetrics } from "@/hooks/useAnalyticsMetrics";

export const MetricsCards = () => {
  const { data: metricsData = [], isLoading, error } = useAnalyticsMetrics();

  // Default fallback metrics if no data
  const defaultMetrics = [
    {
      title: "Total Applications",
      value: "0",
      change: "0%",
      icon: Users,
      trend: "up",
    },
    {
      title: "Active Jobs",
      value: "0",  
      change: "0%",
      icon: Briefcase,
      trend: "up",
    },
    {
      title: "Time to Hire",
      value: "0 days",
      change: "0%",
      icon: Clock,
      trend: "down",
    },
    {
      title: "Hiring Rate",
      value: "0%",
      change: "0%",
      icon: TrendingUp,
      trend: "up",
    },
  ];

  // Use the transformed data directly from the hook
  const processedMetrics = metricsData.length > 0
    ? metricsData.map((metric, index) => {
        const defaultMetric = defaultMetrics[index] || defaultMetrics[0]; // Fallback to first metric if index out of bounds
        return {
          ...defaultMetric,
          value: metric.suffix
            ? `${metric.value}${metric.suffix}`
            : metric.value.toString(),
          change: `${metric.change > 0 ? '+' : ''}${metric.change}%`,
          trend: metric.change >= 0 ? "up" : "down"
        };
      })
    : defaultMetrics;

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-24 bg-muted animate-pulse rounded" />
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
              <div className="h-3 w-20 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    console.error("Error loading metrics:", error);
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {processedMetrics.map((metric, index) => {
        const Icon = metric.icon;
        return (
          <Card key={`${metric.title}-${index}`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {metric.title}
              </CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <p className={`text-xs ${
                metric.trend === "up" ? "text-green-600" : "text-red-600"
              }`}>
                {metric.change} from last month
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
