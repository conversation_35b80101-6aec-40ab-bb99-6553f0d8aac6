
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Calendar, Download, Filter } from "lucide-react";
import { DashboardFilters } from "./DashboardFilters";
import { useDashboardFilters } from "@/contexts/DashboardFiltersContext";

export function DashboardHeader() {
  const { componentVisibility } = useDashboardFilters();

  // Count visible components for display
  const visibleCount = Object.values(componentVisibility).filter(Boolean).length;
  const totalCount = Object.keys(componentVisibility).length;

  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      <div className="min-w-0">
        <h1 className="text-2xl sm:text-3xl font-bold">Dashboard</h1>
        <p className="text-sm sm:text-base text-muted-foreground">
          Welcome back! Here's your recruitment overview.
          {visibleCount < totalCount && (
            <span className="ml-2 text-xs bg-muted px-2 py-1 rounded">
              {visibleCount}/{totalCount} components shown
            </span>
          )}
        </p>
      </div>
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 w-full md:w-auto">
        <Select defaultValue="today">
          <SelectTrigger className="w-full sm:w-[180px]">
            <Calendar className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="quarter">This Quarter</SelectItem>
            <SelectItem value="year">This Year</SelectItem>
          </SelectContent>
        </Select>
        <div className="flex gap-2">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" className="flex-1 sm:flex-initial">
                <Filter className="mr-1 sm:mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Filters</span>
                {visibleCount < totalCount && (
                  <span className="ml-1 sm:ml-2 bg-primary text-primary-foreground text-xs px-1.5 py-0.5 rounded-full">
                    {visibleCount}
                  </span>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Dashboard Filters</SheetTitle>
              </SheetHeader>
              <DashboardFilters />
            </SheetContent>
          </Sheet>
          <Button variant="outline" className="flex-1 sm:flex-initial">
            <Download className="mr-1 sm:mr-2 h-4 w-4" />
            <span className="hidden sm:inline">Export</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
