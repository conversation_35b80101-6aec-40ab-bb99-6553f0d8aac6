
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Users, Briefcase, Calendar, TrendingUp } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useCandidates } from "@/hooks/useCandidates";
import { useJobs } from "@/hooks/useJobs";
import { useDataInvalidation } from "@/hooks/useGlobalInvalidation";

export function DashboardStats() {
  // Set up global invalidation for cross-component consistency
  useDataInvalidation(
    ['candidates', 'jobs'], 
    (changedType, event) => {
      console.log(`📊 Dashboard refreshing due to ${changedType} change:`, event.reason);
      // The unified hooks will automatically update via real-time subscriptions
    },
    { debounceMs: 200 }
  );

  // Use unified real-time hooks
  const { data: candidates = [], isLoading: candidatesLoading } = useCandidates();
  const { data: jobs = [], isLoading: jobsLoading } = useJobs();

  const isLoading = candidatesLoading || jobsLoading;

  const stats = [
    {
      title: "Total Candidates",
      value: candidates?.length || 0,
      change: "+12%",
      icon: Users,
      trend: "up",
    },
    {
      title: "Active Jobs",
      value: jobs?.filter(job => job.is_active).length || 0,
      change: "+3",
      icon: Briefcase,
      trend: "up",
    },
    {
      title: "This Week",
      value: "28",
      change: "-2",
      icon: Calendar,
      trend: "down",
    },
    {
      title: "Avg. Score",
      value: candidates?.length 
        ? Math.round(candidates.reduce((acc, c) => acc + (c.relationshipScore || 0), 0) / candidates.length) + "%"
        : "0%",
      change: "+5%",
      icon: TrendingUp,
      trend: "up",
    },
  ];

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-1" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => (
        <Card key={stat.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <stat.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className={`text-xs ${
              stat.trend === "up" ? "text-green-500" : "text-red-500"
            }`}>
              {stat.change} from last period
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
