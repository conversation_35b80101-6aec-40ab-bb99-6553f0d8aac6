import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Clock, Briefcase, Users, Star, TrendingUp } from "lucide-react";

interface SearchSuggestionsProps {
  onSelect: (query: string) => void;
}

export function SearchSuggestions({ onSelect }: SearchSuggestionsProps) {
  const recentSearches = [
    "Frontend Developer",
    "Product Manager",
    "Remote positions",
    "Senior Engineer"
  ];

  const trendingSearches = [
    "React Developer",
    "DevOps Engineer",
    "Machine Learning",
    "UI/UX Designer"
  ];

  const popularSkills = [
    "TypeScript",
    "React",
    "Node.js",
    "Python"
  ];

  return (
    <div className="grid gap-6 md:grid-cols-3">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Recent Searches
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {recentSearches.map((search) => (
              <Button
                key={search}
                variant="outline"
                size="sm"
                onClick={() => onSelect(search)}
              >
                {search}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Trending Now
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {trendingSearches.map((search) => (
              <Button
                key={search}
                variant="outline"
                size="sm"
                onClick={() => onSelect(search)}
              >
                {search}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-4 w-4" />
            Popular Skills
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {popularSkills.map((skill) => (
              <Button
                key={skill}
                variant="outline"
                size="sm"
                onClick={() => onSelect(skill)}
              >
                {skill}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}