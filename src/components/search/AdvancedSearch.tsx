import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SearchFilters } from "@/types/search";
import { Plus, X } from "lucide-react";

interface AdvancedSearchProps {
  filters: SearchFilters;
  onChange: (filters: SearchFilters) => void;
}

export function AdvancedSearch({ filters, onChange }: AdvancedSearchProps) {
  const [newSkill, setNewSkill] = useState("");

  const handleAddSkill = () => {
    if (!newSkill) return;
    onChange({
      ...filters,
      skills: [
        ...(filters.skills || []),
        { name: newSkill, required: true }
      ]
    });
    setNewSkill("");
  };

  const handleRemoveSkill = (skillName: string) => {
    onChange({
      ...filters,
      skills: filters.skills?.filter(skill => skill.name !== skillName)
    });
  };

  const handleSkillProficiencyChange = (skillName: string, proficiency: 'beginner' | 'intermediate' | 'expert') => {
    onChange({
      ...filters,
      skills: filters.skills?.map(skill => 
        skill.name === skillName 
          ? { ...skill, proficiency } 
          : skill
      )
    });
  };

  return (
    <Card>
      <CardContent className="p-4 space-y-4 sm:space-y-6">
        <div className="space-y-2">
          <Label>Location</Label>
          <div className="flex flex-col sm:flex-row gap-4">
            <Input
              placeholder="Enter location"
              value={filters.location?.address || ""}
              onChange={(e) => onChange({
                ...filters,
                location: {
                  address: e.target.value,
                  radius: filters.location?.radius || 25
                }
              })}
              className="flex-1"
            />
            <div className="w-full sm:w-48">
              <Label>Radius (miles)</Label>
              <Slider
                value={[filters.location?.radius || 25]}
                min={5}
                max={100}
                step={5}
                onValueChange={([value]) => onChange({
                  ...filters,
                  location: {
                    address: filters.location?.address || "",
                    radius: value
                  }
                })}
              />
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label>Skills</Label>
          <div className="flex flex-col sm:flex-row gap-2">
            <Input
              placeholder="Add a skill"
              value={newSkill}
              onChange={(e) => setNewSkill(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleAddSkill()}
              className="flex-1"
            />
            <Button onClick={handleAddSkill} size="icon" className="shrink-0">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {filters.skills?.map((skill) => (
              <div
                key={skill.name}
                className="flex flex-col sm:flex-row items-start sm:items-center gap-2 bg-secondary p-2 rounded-md min-w-0"
              >
                <span className="truncate text-sm">{skill.name}</span>
                <Select
                  value={skill.proficiency || 'any'}
                  onValueChange={(value: any) => handleSkillProficiencyChange(skill.name, value)}
                >
                  <SelectTrigger className="h-7 w-full sm:w-32">
                    <SelectValue placeholder="Proficiency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any">Any Level</SelectItem>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="expert">Expert</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 shrink-0"
                  onClick={() => handleRemoveSkill(skill.name)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-3 sm:space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="remote-only"
              checked={filters.remoteOnly}
              onCheckedChange={(checked) => onChange({
                ...filters,
                remoteOnly: checked
              })}
            />
            <Label htmlFor="remote-only" className="text-sm sm:text-base">Remote Only</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="visa-sponsor"
              checked={filters.visaSponsor}
              onCheckedChange={(checked) => onChange({
                ...filters,
                visaSponsor: checked
              })}
            />
            <Label htmlFor="visa-sponsor" className="text-sm sm:text-base">Visa Sponsorship Available</Label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}