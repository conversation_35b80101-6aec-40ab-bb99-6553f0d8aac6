/**
 * Experience Check Condition Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';
import { supabase } from '@/integrations/supabase/client';

export class ExperienceCheckExecutor extends BaseExecutor {
  id = 'experience-check';
  name = 'Experience Check';
  description = 'Check years of experience';
  category = 'condition' as const;

  configSchema = {
    minYears: {
      type: 'number',
      label: 'Minimum Years of Experience',
      required: true,
      default: 3,
      min: 0,
      max: 50
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId = context.candidateId || context.lastResult?.candidateId;
      const minYears = parseInt(config.minYears || '3', 10);
      
      if (!candidateId && !context.lastResult) {
        return {
          success: true,
          data: { result: false, reason: 'No candidate data available' }
        };
      }

      let yearsOfExperience = 0;

      // Try to get from context first
      if (context.candidateExperience) {
        const match = context.candidateExperience.toString().match(/(\d+)/);
        if (match) {
          yearsOfExperience = parseInt(match[1], 10);
        }
      } else if (candidateId) {
        // Fetch from database
        const { data: candidate, error } = await supabase
          .from('candidates')
          .select('experience, ai_summary')
          .eq('id', candidateId)
          .single();

        if (error) {
          throw new Error(`Failed to fetch candidate: ${error.message}`);
        }

        // Try to extract years from experience field or AI summary
        if (candidate?.experience) {
          const expMatch = candidate.experience.toString().match(/(\d+)\s*(?:years?|yrs?)/i);
          if (expMatch) {
            yearsOfExperience = parseInt(expMatch[1], 10);
          }
        } else if (candidate?.ai_summary) {
          const summaryMatch = candidate.ai_summary.match(/(\d+)\s*(?:years?|yrs?)\s*(?:of\s*)?experience/i);
          if (summaryMatch) {
            yearsOfExperience = parseInt(summaryMatch[1], 10);
          }
        }
      }

      const result = yearsOfExperience >= minYears;

      return {
        success: true,
        data: {
          result,
          yearsOfExperience,
          minYears,
          threshold: minYears
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }
}
