/**
 * Skills Match Condition Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';
import { CandidatesService } from '@/services';
import { generateText } from '@/utils/gemini';

export class SkillsMatchExecutor extends BaseExecutor {
  id = 'skills-match';
  name = 'Skills Match';
  description = 'Check if candidate has required skills';
  category = 'condition' as const;

  configSchema = {
    requiredSkills: {
      type: 'text',
      label: 'Required Skills (comma-separated)',
      required: true,
      placeholder: 'JavaScript, React, Node.js'
    },
    minMatchPercentage: {
      type: 'number',
      label: 'Minimum Match Percentage',
      required: true,
      default: 70,
      min: 0,
      max: 100
    },
    useAIMatching: {
      type: 'boolean',
      label: 'Use AI-Powered Skill Matching',
      required: false,
      default: false,
      description: 'Enable intelligent skill matching that considers related skills and experience context'
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId = context.candidateId || context.lastResult?.candidateId;
      
      if (!candidateId && !context.lastResult) {
        return {
          success: true,
          data: { result: false, reason: 'No candidate data available' }
        };
      }

      const requiredSkills = config.requiredSkills?.split(',')?.map((s: string) => s.trim()) || [];
      const minMatchPercentage = parseInt(config.minMatchPercentage || '70', 10);
      const useAIMatching = config.useAIMatching || false;

      // If we have a previous AI screening result, use that
      if (context.lastResult && context.lastResult.score !== undefined) {
        const result = context.lastResult.score >= minMatchPercentage;
        return {
          success: true,
          data: {
            result,
            score: context.lastResult.score,
            threshold: minMatchPercentage
          }
        };
      }

      // Otherwise, check candidate skills
      if (candidateId) {
        const candidate = await CandidatesService.getCandidate(candidateId);

        if (!candidate) {
          throw new Error('Candidate not found');
        }

        const candidateSkills = candidate.skills || [];

        let matchPercentage: number;
        let matchCount: number;
        let aiAnalysis: string | undefined;

        if (useAIMatching && requiredSkills.length > 0) {
          // Use AI-powered skill matching
          try {
            const candidateSkillsStr = candidateSkills.map((skill: any) =>
              typeof skill === 'string' ? skill : skill.name || skill
            ).join(', ');

            const prompt = `
Analyze the skill match between required skills and candidate skills:

Required Skills: ${requiredSkills.join(', ')}
Candidate Skills: ${candidateSkillsStr}
Candidate Experience: ${candidate.experience || 'Not specified'}

Consider:
1. Direct skill matches
2. Related/transferable skills
3. Experience level and context
4. Technology stack compatibility

Provide a match percentage (0-100) and brief analysis.

Return in JSON format:
{
  "matchPercentage": 85,
  "analysis": "Strong match with direct experience in most required skills. React and Node.js experience directly applicable. JavaScript expertise evident."
}
`;

            const systemPrompt = "You are an expert technical recruiter specializing in skill assessment. Provide accurate, objective skill matching analysis.";

            const response = await generateText(prompt, systemPrompt);
            const cleanedResponse = response.replace(/```json\s*/, '').replace(/```\s*$/, '').trim();
            const aiResult = JSON.parse(cleanedResponse);

            matchPercentage = aiResult.matchPercentage || 0;
            aiAnalysis = aiResult.analysis;
            matchCount = Math.round((matchPercentage / 100) * requiredSkills.length);
          } catch (error) {
            console.error('AI skill matching failed, falling back to basic matching:', error);
            // Fall back to basic matching
            matchCount = 0;
            requiredSkills.forEach((requiredSkill: string) => {
              const found = candidateSkills.some((skill: any) => {
                if (typeof skill === 'string') {
                  return skill.toLowerCase().includes(requiredSkill.toLowerCase());
                } else if (skill.name) {
                  return skill.name.toLowerCase().includes(requiredSkill.toLowerCase());
                }
                return false;
              });

              if (found) matchCount++;
            });

            matchPercentage = requiredSkills.length > 0
              ? (matchCount / requiredSkills.length) * 100
              : 0;
          }
        } else {
          // Use basic skill matching
          matchCount = 0;
          requiredSkills.forEach((requiredSkill: string) => {
            const found = candidateSkills.some((skill: any) => {
              if (typeof skill === 'string') {
                return skill.toLowerCase().includes(requiredSkill.toLowerCase());
              } else if (skill.name) {
                return skill.name.toLowerCase().includes(requiredSkill.toLowerCase());
              }
              return false;
            });

            if (found) matchCount++;
          });

          matchPercentage = requiredSkills.length > 0
            ? (matchCount / requiredSkills.length) * 100
            : 0;
        }

        const result = matchPercentage >= minMatchPercentage;

        return {
          success: true,
          data: {
            result,
            matchPercentage,
            matchedSkills: matchCount,
            totalRequired: requiredSkills.length,
            threshold: minMatchPercentage,
            aiAnalysis,
            useAIMatching
          }
        };
      }

      // Default to false if no data available
      return {
        success: true,
        data: { result: false, reason: 'Unable to evaluate skills' }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }
}
