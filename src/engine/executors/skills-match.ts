/**
 * Skills Match Condition Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';
import { CandidatesService } from '@/services';

export class SkillsMatchExecutor extends BaseExecutor {
  id = 'skills-match';
  name = 'Skills Match';
  description = 'Check if candidate has required skills';
  category = 'condition' as const;

  configSchema = {
    requiredSkills: {
      type: 'text',
      label: 'Required Skills (comma-separated)',
      required: true,
      placeholder: 'JavaScript, React, Node.js'
    },
    minMatchPercentage: {
      type: 'number',
      label: 'Minimum Match Percentage',
      required: true,
      default: 70,
      min: 0,
      max: 100
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId = context.candidateId || context.lastResult?.candidateId;
      
      if (!candidateId && !context.lastResult) {
        return {
          success: true,
          data: { result: false, reason: 'No candidate data available' }
        };
      }

      const requiredSkills = config.requiredSkills?.split(',')?.map((s: string) => s.trim()) || [];
      const minMatchPercentage = parseInt(config.minMatchPercentage || '70', 10);

      // If we have a previous AI screening result, use that
      if (context.lastResult && context.lastResult.score !== undefined) {
        const result = context.lastResult.score >= minMatchPercentage;
        return {
          success: true,
          data: {
            result,
            score: context.lastResult.score,
            threshold: minMatchPercentage
          }
        };
      }

      // Otherwise, check candidate skills
      if (candidateId) {
        const candidate = await CandidatesService.getCandidate(candidateId);

        if (!candidate) {
          throw new Error('Candidate not found');
        }

        const candidateSkills = candidate.skills || [];
        
        // Calculate match percentage
        let matchCount = 0;
        requiredSkills.forEach((requiredSkill: string) => {
          const found = candidateSkills.some((skill: any) => {
            if (typeof skill === 'string') {
              return skill.toLowerCase().includes(requiredSkill.toLowerCase());
            } else if (skill.name) {
              return skill.name.toLowerCase().includes(requiredSkill.toLowerCase());
            }
            return false;
          });
          
          if (found) matchCount++;
        });
        
        const matchPercentage = requiredSkills.length > 0 
          ? (matchCount / requiredSkills.length) * 100 
          : 0;
        
        const result = matchPercentage >= minMatchPercentage;

        return {
          success: true,
          data: {
            result,
            matchPercentage,
            matchedSkills: matchCount,
            totalRequired: requiredSkills.length,
            threshold: minMatchPercentage
          }
        };
      }

      // Default to false if no data available
      return {
        success: true,
        data: { result: false, reason: 'Unable to evaluate skills' }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }
}
