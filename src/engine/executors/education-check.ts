/**
 * Education Check Condition Executor
 */

import { BaseExecutor } from '../BaseExecutor';
import { NodeExecutorResult, ExecutionContext } from '../types';
import { supabase } from '@/integrations/supabase/client';

export class EducationCheckExecutor extends BaseExecutor {
  id = 'education-check';
  name = 'Education Check';
  description = 'Verify education requirements';
  category = 'condition' as const;

  configSchema = {
    minDegree: {
      type: 'string',
      label: 'Minimum Degree Required',
      required: true,
      options: ['high-school', 'bachelors', 'masters', 'phd'],
      default: 'bachelors'
    }
  };

  protected async executeInternal(
    nodeData: any,
    context: ExecutionContext
  ): Promise<NodeExecutorResult> {
    try {
      const config = nodeData.config || {};
      const candidateId = context.candidateId || context.lastResult?.candidateId;
      const minDegree = config.minDegree || 'bachelors';
      
      if (!candidateId && !context.lastResult) {
        return {
          success: true,
          data: { result: false, reason: 'No candidate data available' }
        };
      }

      const degreeHierarchy = {
        'high-school': 1,
        'bachelors': 2,
        'masters': 3,
        'phd': 4
      };

      const minDegreeLevel = degreeHierarchy[minDegree] || 2;
      let candidateDegreeLevel = 0;
      let candidateDegree = 'unknown';

      if (candidateId) {
        // Fetch from database
        const { data: candidate, error } = await supabase
          .from('candidates')
          .select('education, ai_summary')
          .eq('id', candidateId)
          .single();

        if (error) {
          throw new Error(`Failed to fetch candidate: ${error.message}`);
        }

        // Try to extract degree from education field or AI summary
        const educationText = (candidate?.education || candidate?.ai_summary || '').toLowerCase();
        
        if (educationText.includes('phd') || educationText.includes('doctorate')) {
          candidateDegreeLevel = 4;
          candidateDegree = 'phd';
        } else if (educationText.includes('master') || educationText.includes('mba') || educationText.includes('m.s') || educationText.includes('m.a')) {
          candidateDegreeLevel = 3;
          candidateDegree = 'masters';
        } else if (educationText.includes('bachelor') || educationText.includes('b.s') || educationText.includes('b.a') || educationText.includes('undergraduate')) {
          candidateDegreeLevel = 2;
          candidateDegree = 'bachelors';
        } else if (educationText.includes('high school') || educationText.includes('diploma')) {
          candidateDegreeLevel = 1;
          candidateDegree = 'high-school';
        }
      }

      const result = candidateDegreeLevel >= minDegreeLevel;

      return {
        success: true,
        data: {
          result,
          candidateDegree,
          candidateDegreeLevel,
          minDegree,
          minDegreeLevel,
          threshold: minDegree
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error
      };
    }
  }
}
